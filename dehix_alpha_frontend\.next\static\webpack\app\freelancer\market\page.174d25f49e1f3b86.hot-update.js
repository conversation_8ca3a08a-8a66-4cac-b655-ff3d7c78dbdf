"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/market/page",{

/***/ "(app-pages-browser)/./src/components/shared/JobCard.tsx":
/*!*******************************************!*\
  !*** ./src/components/shared/JobCard.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Heart_MoreVertical_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Heart,MoreVertical!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Heart_MoreVertical_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Heart,MoreVertical!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Heart_MoreVertical_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Heart,MoreVertical!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Heart_MoreVertical_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Heart,MoreVertical!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ProjectDrawer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ProjectDrawer */ \"(app-pages-browser)/./src/components/shared/ProjectDrawer.tsx\");\n/* harmony import */ var _components_report_tabs_NewReportTabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/report-tabs/NewReportTabs */ \"(app-pages-browser)/./src/components/report-tabs/NewReportTabs.tsx\");\n/* harmony import */ var _utils_getReporttypeFromPath__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/getReporttypeFromPath */ \"(app-pages-browser)/./src/utils/getReporttypeFromPath.tsx\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _lib_projectDraftSlice__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/projectDraftSlice */ \"(app-pages-browser)/./src/lib/projectDraftSlice.ts\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Simple loader/spinner component (you can replace with your own)\nconst Loader = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-5 h-5 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined);\n_c = Loader;\nconst JobCard = (param)=>{\n    let { job, onApply, onNotInterested, bidExist } = param;\n    _s();\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useDispatch)();\n    const draftedProjects = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.projectDraft.draftedProjects);\n    const isDrafted = draftedProjects === null || draftedProjects === void 0 ? void 0 : draftedProjects.includes(job._id);\n    const user = (0,react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector)((state)=>state.user);\n    const toggleExpand = ()=>setExpanded(!expanded);\n    const [openReport, setOpenReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [menuOpen, setMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const reportType = (0,_utils_getReporttypeFromPath__WEBPACK_IMPORTED_MODULE_9__.getReportTypeFromPath)(pathname);\n    const reportData = {\n        subject: \"\",\n        description: \"\",\n        report_role: (user === null || user === void 0 ? void 0 : user.type) || \"STUDENT\",\n        report_type: reportType,\n        status: \"OPEN\",\n        reportedbyId: (user === null || user === void 0 ? void 0 : user.uid) || \"user123\",\n        reportedId: job._id\n    };\n    const handleLike = async ()=>{\n        setLoading(true); // start loading\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_10__.axiosInstance.put(\"/freelancer/draft\", {\n                project_id: job._id\n            });\n            if (response.status === 200) {\n                dispatch((0,_lib_projectDraftSlice__WEBPACK_IMPORTED_MODULE_11__.addDraftedProject)(job._id));\n            }\n        } catch (error) {\n            console.error(\"Failed to add project to draft:\", error);\n        } finally{\n            setLoading(false); // stop loading\n        }\n    };\n    const handleUnlike = async ()=>{\n        setLoading(true); // start loading\n        try {\n            const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_10__.axiosInstance.delete(\"/freelancer/draft\", {\n                data: {\n                    project_id: job._id\n                }\n            });\n            if (response.status === 200) {\n                dispatch((0,_lib_projectDraftSlice__WEBPACK_IMPORTED_MODULE_11__.removeDraftedProject)(job._id));\n            }\n        } catch (error) {\n            console.error(\"Failed to remove project from draft:\", error);\n        } finally{\n            setLoading(false); // stop loading\n        }\n    };\n    const profile = job.profiles && job.profiles.length > 0 ? job.profiles[0] : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"w-full max-w-5xl mx-auto shadow-sm hover:shadow-md transition-shadow duration-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    className: \"text-xl\",\n                                    children: [\n                                        job.projectName,\n                                        \" \"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                    className: \"mt-1\",\n                                    children: [\n                                        \"Position: \",\n                                        job.position || \"Web developer\",\n                                        \" \\xb7 Exp:\",\n                                        \" \",\n                                        (profile === null || profile === void 0 ? void 0 : profile.years) || \"2\",\n                                        \" yrs\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center gap-3\",\n                            children: [\n                                job.status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: job.status.toLowerCase() === \"pending\" ? \"bg-amber-300/10 text-amber-500 border-amber-500/20\" : \"bg-green-500/10 text-green-500 border-green-500/20\",\n                                    children: job.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, undefined),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loader, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Heart_MoreVertical_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-5 h-5 cursor-pointer \".concat(isDrafted ? \"fill-red-600 text-red-600\" : \"text-gray-400 hover:text-gray-600\"),\n                                    onClick: loading ? undefined : isDrafted ? handleUnlike : handleLike\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"text-gray-500 hover:text-gray-100 p-0 h-6 w-6 focus-visible:ring-0 focus-visible:ring-offset-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Heart_MoreVertical_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            className: \"w-32 z-50\",\n                                            sideOffset: 4,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuItem, {\n                                                onClick: ()=>setOpenReport(true),\n                                                className: \"text-red-600 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                                children: \"Report\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 \".concat(!expanded && \"line-clamp-3\"),\n                        children: job.description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, undefined),\n                    job.description && job.description.length > 150 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleExpand,\n                        className: \"text-primary text-sm mt-1 hover:underline\",\n                        children: expanded ? \"less\" : \"more\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium mb-2\",\n                                children: \"Skills required\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: job.skillsRequired && job.skillsRequired.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"rounded-md\",\n                                        children: skill\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    profile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                children: [\n                                    profile.positions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-primary/10 text-primary px-2 py-1 rounded text-xs\",\n                                        children: [\n                                            profile.positions,\n                                            \" Positions\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    profile.years && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-primary/10 text-primary px-2 py-1 rounded text-xs\",\n                                        children: [\n                                            profile.years,\n                                            \" Years\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, undefined),\n                            profile.connectsRequired && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 text-sm\",\n                                children: [\n                                    \"Connects required:\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: profile.connectsRequired\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        onClick: onNotInterested,\n                        className: \"text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Heart_MoreVertical_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-4 w-4 mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Not Interested\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProjectDrawer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Heart_MoreVertical_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"h-4 w-4 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 17\n                        }, void 0),\n                        project: job,\n                        text: \"View\",\n                        isSizeSmall: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/freelancer/market/project/\".concat(job._id, \"/apply\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            type: \"submit\",\n                            className: \"\",\n                            size: \"sm\",\n                            disabled: bidExist,\n                            children: bidExist ? \"Applied\" : \"Bid\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, undefined),\n            openReport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black bg-opacity-40 flex justify-center items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-900 p-6 rounded-md w-full max-w-lg relative shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setOpenReport(false),\n                            className: \"absolute top-2 right-2 text-gray-400 hover:text-red-500\",\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_report_tabs_NewReportTabs__WEBPACK_IMPORTED_MODULE_8__.NewReportTab, {\n                            reportData: reportData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n                lineNumber: 273,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\shared\\\\JobCard.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, undefined);\n};\n_s(JobCard, \"g4wPxGF+oWImdcrnoMEbdae4nLY=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useDispatch,\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_13__.useSelector,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c1 = JobCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (JobCard);\nvar _c, _c1;\n$RefreshReg$(_c, \"Loader\");\n$RefreshReg$(_c1, \"JobCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/JobCard.tsx\n"));

/***/ })

});