import { Service } from "fastify-decorators";
import { Model } from "mongoose";
import { BaseDAO } from "../common/base.dao";
import { IInterview2, InterviewModel } from "../models/interview.entity";
import { v4 as uuid4 } from "uuid";
import { FreelancerModel, IFreelancer } from "../models/freelancer.entity";
import { ISkill, SkillModel } from "../models/skills.entity";
import { DomainModel, IDomain } from "../models/domain.entity";

@Service()
export class InterviewDao extends BaseDAO {
  model: Model<IInterview2>;
  freelancerModel: Model<IFreelancer>;
  skillModel: Model<ISkill>;
  domainModel: Model<IDomain>;
  constructor() {
    super();
    this.model = InterviewModel;
    this.freelancerModel = FreelancerModel;
    this.skillModel = SkillModel;
    this.domainModel = DomainModel;
  }

  async createInterview(data: string) {
    return this.model.create(data);
  }

  async getInterviewById(interviewId: string) {
    return this.model.findById(interviewId);
  }

  async getInterviewBidsByInterviewId(interviewee_id: string) {
    return this.model.find({ interviewee_id });
  }

  async getInterviewByInterviewerId(interviewerId: string) {
    const interviews = await this.model.find({ interviewerId }).lean();

    const { interviewerIds } = this.extractIds(interviews);
    const freelancerMap = await this.getFreelancerInfo(interviewerIds);

    for (const interview of interviews) {
      if (interview.talentId && interview.talentType) {
        interview.talentId = await this.getTalentInfo(
          interview.talentId,
          interview.talentType,
        );
      }
    }

    this.attachDetails(interviews, freelancerMap);

    return interviews;
  }

  async getInterviewByIntervieweeId(intervieweeId: string) {
    const interviews = await this.model.find({ intervieweeId }).lean();

    const { interviewerIds } = this.extractIds(interviews);
    const freelancerMap = await this.getFreelancerInfo(interviewerIds);

    for (const interview of interviews) {
      if (interview.talentId && interview.talentType) {
        interview.talentId = await this.getTalentInfo(
          interview.talentId,
          interview.talentType,
        );
      }
    }

    this.attachDetails(interviews, freelancerMap);

    return interviews;
  }

  async getInterviewByCreatorId(creatorId: string) {
    const interviews = await this.model.find({ creatorId }).lean();

    const { interviewerIds } = this.extractIds(interviews);
    const freelancerMap = await this.getFreelancerInfo(interviewerIds);

    for (const interview of interviews) {
      if (interview.talentId && interview.talentType) {
        interview.talentId = await this.getTalentInfo(
          interview.talentId,
          interview.talentType,
        );
      }
    }

    this.attachDetails(interviews, freelancerMap);

    return interviews;
  }

  async getAllInterviews(query: any, page: string, limit: string) {
    const pages = parseInt(page) - 1;
    const pageSize = parseInt(limit);
    const pageIndex = pages * pageSize;

    const interviews = await this.model
      .find(query)
      .skip(pageIndex)
      .limit(pageSize)
      .lean();

    const { interviewerIds } = this.extractIds(interviews);
    const freelancerMap = await this.getFreelancerInfo(interviewerIds);

    for (const interview of interviews) {
      // Populate talent details
      if (interview.talentId && interview.talentType) {
        (interview as any).talentDetails = await this.getTalentInfo(
          interview.talentId,
          interview.talentType,
        );
      }

      // Ensure interviewBids is an array for the frontend
      // Always send interviewBids as a plain array so the frontend can safely consume it
      if (
        interview.interviewBids &&
        typeof interview.interviewBids === "object" &&
        Object.keys(interview.interviewBids).length > 0
      ) {
        interview.interviewBids =
          interview.interviewBids instanceof Map
            ? Array.from(interview.interviewBids.values())
            : Object.values(interview.interviewBids as any);
      } else {
        interview.interviewBids = [];
      }
    }

    this.attachDetails(interviews, freelancerMap);

    return interviews;
  }

  private extractIds(interviews: any[]) {
    const interviewerIds = new Set<string>();

    for (const interview of interviews) {
      if (Array.isArray(interview.interviewBids)) {
        for (const bid of interview.interviewBids) {
          if (bid?.interviewerId) {
            interviewerIds.add(bid.interviewerId);
          }
        }
      }
    }

    return { interviewerIds: Array.from(interviewerIds) };
  }

  private async getFreelancerInfo(interviewerIds: string[]) {
    const freelancers = await this.freelancerModel
      .find({ _id: { $in: interviewerIds } })
      .select("userName skills workExperience")
      .populate({ path: "skills", select: "name" })
      .lean();

    return new Map(
      freelancers.map((freelancer) => [
        freelancer._id.toString(),
        {
          ...freelancer,
          skills: freelancer.skills
            ? [...new Set(freelancer.skills.map((skill) => skill.name))]
            : [],
        },
      ]),
    );
  }

  private async getTalentInfo(talentId: string, talentType: string) {
    let talent;
    if (talentType === "SKILL") {
      talent = await this.skillModel
        .findById(talentId)
        .select("_id label")
        .lean();
    } else if (talentType === "DOMAIN") {
      talent = await this.domainModel
        .findById(talentId)
        .select("_id label")
        .lean();
    }

    // Map talentId -> Talent info (including ID)
    return talent
      ? { id: talent._id.toString(), label: talent.label, type: talentType }
      : null;
  }

  private attachDetails(interviews: any[], freelancerMap: Map<string, any>) {
    for (const interview of interviews) {
      if (Array.isArray(interview.interviewBids)) {
        for (const bid of interview.interviewBids) {
          if (bid?.interviewerId && freelancerMap.has(bid.interviewerId)) {
            bid.interviewer = freelancerMap.get(bid.interviewerId);
          }
        }
      }
    }
  }

  async getInterviewsByTalentIds(talentIds: string[]) {
    return await this.model.find({
      talentId: { $in: talentIds },
    });
  }

  async updateInterviewById(interview_id: string, update: any) {
    return this.model.findOneAndUpdate({ _id: interview_id }, update, {
      new: true,
    });
  }

  async deleteInterviewById(interviewId: string) {
    return this.model.findOneAndDelete({ _id: interviewId });
  }

  async getInterviewByRating(rating: number) {
    return this.model.find({ rating: rating });
  }

  async getInterviewBidsByInterviewBidId(interviewId: string, bidId: string) {
    // Fetch only interviewBids field to keep the document light.
    const doc = await this.model
      .findOne({ _id: interviewId })
      .select("interviewBids")
      .lean();
    if (!doc || !doc.interviewBids) return null;

    const bidsField = doc.interviewBids as any;

    // Case 1: Map (BSON Map comes back as JS Map after lean = object), treat as object
    if (bidsField instanceof Map) {
      return bidsField.get(bidId) ?? null;
    }

    // Case 2: Plain object keyed by bidId
    if (!Array.isArray(bidsField) && typeof bidsField === "object") {
      return (bidsField as Record<string, any>)[bidId] ?? null;
    }

    // Case 3: Array of bids
    if (Array.isArray(bidsField)) {
      return bidsField.find((b: any) => b._id === bidId) ?? null;
    }

    return null;
  }

  async getAllInterviewBidsByInterviewerId(interviewerId: string) {
    return this.model.find({ "interviewBids.interviewerId": interviewerId });
  }

  async createInterviewBid(interviewId: string, data: any) {
    const bidId = uuid4();
    const updateInterview = await this.model.findByIdAndUpdate(
      interviewId,
      {
        $set: {
          [`interviewBids.${bidId}`]: {
            _id: bidId,
            ...data,
          },
        },
      },
      { new: true, upsert: true },
    );
    return updateInterview.interviewBids!.get(bidId);
  }

  async updateInterviewBid(interviewId: string, bidId: string, data: any) {
    const updateData = Object.entries(data).reduce((acc, [key, value]) => {
      acc[`interviewBids.${bidId}.${key}`] = value;
      return acc;
    }, {});

    const updatedInterview = await this.model.findOneAndUpdate(
      {
        _id: interviewId,
        [`interviewBids.${bidId}`]: { $exists: true },
      },
      { $set: updateData },
      { new: true },
    );

    const updatedBid = updatedInterview!.interviewBids!.get(bidId);
    return updatedBid;
  }

  async getInterviewBidsByIntervieweeId(intervieweeId: string) {
    // Fetch only interviewBids field for interviews where intervieweeId matches
    const interviews = await this.model
      .find({ intervieweeId })
      .select("interviewBids")
      .lean();

    const bids: any[] = [];
    for (const interview of interviews) {
      if (interview.interviewBids) {
        if (interview.interviewBids instanceof Map) {
          interview.interviewBids.forEach((value: any) => {
            if (value) bids.push(value);
          });
        } else {
          const bidValues = Object.values(
            interview.interviewBids as Record<string, any>,
          );
          bids.push(...bidValues);
        }
      }
    }
    return bids;
  }

  async deleteInterviewBid(interviewId: string, bidId: string) {
    return this.model.findOneAndUpdate(
      { _id: interviewId },
      { $unset: { [`interviewBids.${bidId}`]: 1 } },
      { new: true },
    );
  }

  /**
   * Mark the provided bid as ACCEPTED, set interviewerId on the interview,
   * and mark any *other* bids from the same interviewer on that interview as REJECTED.
   * Supports interviewBids stored as Map, plain object, or array.
   */
  async selectInterviewBid(interviewId: string, bid: any) {
    // Load full document (not lean) so we can mutate safely
    const interview: any = await this.model.findById(interviewId);
    if (!interview) return null;

    // Ensure interviewBids exists
    if (!interview.interviewBids) {
      interview.interviewBids = new Map();
    }

    // Helper to normalise all bids into an array we can mutate
    const toArray = (bidsField: any): any[] => {
      if (bidsField instanceof Map) return Array.from(bidsField.values());
      if (Array.isArray(bidsField)) return bidsField;
      if (typeof bidsField === "object") return Object.values(bidsField);
      return [];
    };

    const bidsArr = toArray(interview.interviewBids);

    // Update statuses
    bidsArr.forEach((b: any) => {
      if (b._id === bid._id) {
        b.status = "ACCEPTED";
      } else if (b.interviewerId === bid.interviewerId) {
        // Same interviewer but another bid – mark rejected to avoid future 500s
        b.status = "REJECTED";
      }
    });

    // Persist changes back depending on original structure
    const originalField = interview.interviewBids;
    if (originalField instanceof Map) {
      // Clear and repopulate map
      interview.interviewBids = new Map();
      bidsArr.forEach((b: any) => {
        interview.interviewBids.set(b._id, b);
      });
    } else if (Array.isArray(originalField)) {
      interview.interviewBids = bidsArr;
    } else if (typeof originalField === "object") {
      const obj: Record<string, any> = {};
      bidsArr.forEach((b: any) => {
        obj[b._id] = b;
      });
      interview.interviewBids = obj;
    }

    // Set chosen interviewer on root document
    interview.interviewerId = bid.interviewerId;

    await interview.save();
    return interview;
  }
}
